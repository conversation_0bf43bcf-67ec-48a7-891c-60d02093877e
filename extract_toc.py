#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速提取Word文档目录的脚本
"""

import os
from docx import Document

def extract_headings(file_path):
    """
    提取文档中的所有标题
    """
    try:
        doc = Document(file_path)
        
        print(f"{'='*60}")
        print(f"文档: {os.path.basename(file_path)} - 目录结构")
        print(f"{'='*60}")
        
        headings = []
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            style_name = paragraph.style.name if paragraph.style else "Normal"
            
            # 查找标题样式
            if style_name.startswith('Heading') or style_name in ['Title']:
                level = 1
                if 'Heading' in style_name:
                    try:
                        level = int(style_name.split()[-1])
                    except:
                        level = 1
                elif style_name == 'Title':
                    level = 0
                
                if text and text != "目录":  # 排除"目录"标题本身
                    headings.append({
                        'level': level,
                        'text': text,
                        'style': style_name
                    })
        
        # 显示标题结构
        print("文档目录结构：")
        print("-" * 60)
        for heading in headings:
            indent = "  " * (heading['level'] - 1) if heading['level'] > 0 else ""
            print(f"{indent}{heading['text']}")
        
        print(f"\n{'='*60}")
        print(f"共找到 {len(headings)} 个标题")
        
        return headings
        
    except Exception as e:
        print(f"提取标题时出错: {str(e)}")
        return []

if __name__ == "__main__":
    file_path = "测评报告.docx"
    if os.path.exists(file_path):
        extract_headings(file_path)
    else:
        print(f"文件不存在: {file_path}")
