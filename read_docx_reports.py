#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
读取Word文档测评报告的Python脚本
保留原有格式并提取内容
"""

import os
from docx import Document
from docx.shared import Inches
import sys

def read_docx_content(file_path):
    """
    读取docx文件内容，保留格式信息
    """
    try:
        doc = Document(file_path)
        content = []
        
        print(f"\n{'='*60}")
        print(f"文档: {os.path.basename(file_path)}")
        print(f"{'='*60}")
        
        # 读取文档的基本信息
        print(f"段落数量: {len(doc.paragraphs)}")
        print(f"表格数量: {len(doc.tables)}")
        
        # 读取所有段落
        print(f"\n{'='*40} 文档内容 {'='*40}")
        
        for i, paragraph in enumerate(doc.paragraphs):
            if paragraph.text.strip():  # 只显示非空段落
                # 获取段落样式信息
                style_name = paragraph.style.name if paragraph.style else "Normal"
                
                print(f"\n[段落 {i+1}] (样式: {style_name})")
                print(f"内容: {paragraph.text}")
                
                # 检查段落中的格式
                for run in paragraph.runs:
                    if run.bold or run.italic or run.underline:
                        format_info = []
                        if run.bold:
                            format_info.append("粗体")
                        if run.italic:
                            format_info.append("斜体")
                        if run.underline:
                            format_info.append("下划线")
                        print(f"  格式化文本: '{run.text}' ({', '.join(format_info)})")
        
        # 读取表格内容
        if doc.tables:
            print(f"\n{'='*40} 表格内容 {'='*40}")
            for table_idx, table in enumerate(doc.tables):
                print(f"\n[表格 {table_idx + 1}]")
                print(f"行数: {len(table.rows)}, 列数: {len(table.columns) if table.rows else 0}")
                
                for row_idx, row in enumerate(table.rows):
                    row_data = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        row_data.append(cell_text if cell_text else "[空]")
                    print(f"第{row_idx + 1}行: {' | '.join(row_data)}")
        
        return True
        
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")
        return False

def main():
    """
    主函数：读取目录下的所有docx文件
    """
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")
    
    # 查找所有docx文件
    docx_files = []
    for file in os.listdir(current_dir):
        if file.endswith('.docx') and not file.startswith('~$'):  # 排除临时文件
            docx_files.append(file)
    
    if not docx_files:
        print("未找到任何.docx文件")
        return
    
    print(f"找到 {len(docx_files)} 个Word文档:")
    for file in docx_files:
        print(f"  - {file}")
    
    # 逐个读取文档
    for docx_file in docx_files:
        file_path = os.path.join(current_dir, docx_file)
        success = read_docx_content(file_path)
        if not success:
            print(f"无法读取文件: {docx_file}")

def extract_toc_content(file_path):
    """
    提取文档中的目录内容
    """
    try:
        doc = Document(file_path)

        print(f"\n{'='*60}")
        print(f"文档: {os.path.basename(file_path)} - 目录提取")
        print(f"{'='*60}")

        # 查找"目录"标题的位置
        toc_start_index = -1
        toc_end_index = -1

        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if text == "目录":
                toc_start_index = i
                print(f"找到目录标题在段落 {i+1}")
                break

        if toc_start_index == -1:
            print("未找到'目录'标题")
            return False

        # 查找目录结束位置（通常是下一个主要标题）
        for i in range(toc_start_index + 1, len(doc.paragraphs)):
            paragraph = doc.paragraphs[i]
            text = paragraph.text.strip()
            style_name = paragraph.style.name if paragraph.style else "Normal"

            # 如果遇到Heading 1样式或者特定的结束标志，停止
            if (style_name == "Heading 1" or
                text in ["测评项目概述", "被测对象描述", "单项测评结果分析"] or
                (text and not any(char.isdigit() or char == '.' or char == '…' or char == ' ' for char in text[:5]))):
                toc_end_index = i
                break

        if toc_end_index == -1:
            toc_end_index = len(doc.paragraphs)

        print(f"目录内容范围：段落 {toc_start_index + 2} 到 {toc_end_index}")
        print(f"\n{'='*40} 目录内容 {'='*40}")

        # 由于目录是通过域代码生成的，文本为空，我们需要查找实际的标题内容
        print("目录样式已找到，但内容为空（这是Word自动生成目录的正常现象）")
        print("正在查找文档中的实际标题结构...\n")

        # 查找文档中的标题结构
        headings = []
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            style_name = paragraph.style.name if paragraph.style else "Normal"

            # 查找标题样式
            if style_name.startswith('Heading') or style_name in ['Title', 'Subtitle']:
                level = 1
                if 'Heading' in style_name:
                    try:
                        level = int(style_name.split()[-1])
                    except:
                        level = 1
                elif style_name == 'Title':
                    level = 0
                elif style_name == 'Subtitle':
                    level = 1

                if text:
                    headings.append({
                        'level': level,
                        'text': text,
                        'style': style_name,
                        'index': i + 1
                    })

        # 显示标题结构
        print("文档标题结构：")
        print("-" * 60)
        for heading in headings:
            indent = "  " * heading['level']
            print(f"{indent}{heading['text']}")

        print(f"\n{'='*60}")
        print(f"共找到 {len(headings)} 个标题")

        return True

    except Exception as e:
        print(f"提取目录时出错: {str(e)}")
        return False

def read_specific_file(filename):
    """
    读取指定的文档文件
    """
    if not filename.endswith('.docx'):
        filename += '.docx'

    if os.path.exists(filename):
        return read_docx_content(filename)
    else:
        print(f"文件不存在: {filename}")
        return False

def extract_toc_from_file(filename):
    """
    从指定文件提取目录
    """
    if not filename.endswith('.docx'):
        filename += '.docx'

    if os.path.exists(filename):
        return extract_toc_content(filename)
    else:
        print(f"文件不存在: {filename}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # 如果提供了文件名参数
        filename = sys.argv[1]
        if len(sys.argv) > 2 and sys.argv[2] == '--toc':
            # 如果有--toc参数，提取目录
            extract_toc_from_file(filename)
        else:
            # 否则读取完整文件
            read_specific_file(filename)
    else:
        # 否则读取所有docx文件
        main()
